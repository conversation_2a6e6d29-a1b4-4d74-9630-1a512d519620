/// User entity representing the core user data
class UserEntity {
  final String id;
  final String phoneNumber;
  final String name;
  final String? email;
  final bool isProfileComplete;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  const UserEntity({
    required this.id,
    required this.phoneNumber,
    required this.name,
    this.email,
    this.isProfileComplete = false,
    this.isVerified = false,
    required this.createdAt,
    this.lastLoginAt,
  });

  // Check if profile has the minimum required information
  bool get hasRequiredInfo => name.isNotEmpty;

  // Create an empty user
  static UserEntity empty() {
    return UserEntity(
      id: '',
      phoneNumber: '',
      name: '',
      createdAt: DateTime.now(),
    );
  }
}

/// User settings entity
class UserSettings {
  final String languageCode;
  final bool sosButtonEnabled;
  final bool shakeToSos;
  final bool voiceActivation;
  final bool autoSendLocation;
  final bool discreteMode;
  final int sosCountdown; // seconds before S<PERSON> is sent
  final bool biometricLock;
  final bool notificationsEnabled;
  final String emergencyMessage;

  const UserSettings({
    required this.languageCode,
    required this.sosButtonEnabled,
    required this.shakeToSos,
    required this.voiceActivation,
    required this.autoSendLocation,
    required this.discreteMode,
    required this.sosCountdown,
    required this.biometricLock,
    required this.notificationsEnabled,
    required this.emergencyMessage,
  });

  factory UserSettings.defaults() {
    return const UserSettings(
      languageCode: 'en',
      sosButtonEnabled: true,
      shakeToSos: true,
      voiceActivation: false,
      autoSendLocation: true,
      discreteMode: false,
      sosCountdown: 5,
      biometricLock: false,
      notificationsEnabled: true,
      emergencyMessage: 'I need help! This is an emergency.',
    );
  }
}
