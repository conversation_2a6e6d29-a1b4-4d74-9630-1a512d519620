// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'fake_call_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$FakeCallEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FakeCallEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent()';
}


}

/// @nodoc
class $FakeCallEventCopyWith<$Res>  {
$FakeCallEventCopyWith(FakeCallEvent _, $Res Function(FakeCallEvent) __);
}


/// Adds pattern-matching-related methods to [FakeCallEvent].
extension FakeCallEventPatterns on FakeCallEvent {
/// A variant of `map` that fallback to returning `orElse`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeMap<TResult extends Object?>({TResult Function( _Initialize value)?  initialize,TResult Function( _SetCallerName value)?  setCallerName,TResult Function( _SetCallerNumber value)?  setCallerNumber,TResult Function( _SetCallerImage value)?  setCallerImage,TResult Function( _SetTimerDuration value)?  setTimerDuration,TResult Function( _SaveCallerDetails value)?  saveCallerDetails,TResult Function( _StartFakeCall value)?  startFakeCall,TResult Function( _CancelFakeCall value)?  cancelFakeCall,TResult Function( _ShowIncomingCall value)?  showIncomingCall,TResult Function( _AnswerCall value)?  answerCall,TResult Function( _DeclineCall value)?  declineCall,TResult Function( _EndCall value)?  endCall,TResult Function( _UpdateCallDuration value)?  updateCallDuration,TResult Function( _Reset value)?  reset,required TResult orElse(),}){
final _that = this;
switch (_that) {
case _Initialize() when initialize != null:
return initialize(_that);case _SetCallerName() when setCallerName != null:
return setCallerName(_that);case _SetCallerNumber() when setCallerNumber != null:
return setCallerNumber(_that);case _SetCallerImage() when setCallerImage != null:
return setCallerImage(_that);case _SetTimerDuration() when setTimerDuration != null:
return setTimerDuration(_that);case _SaveCallerDetails() when saveCallerDetails != null:
return saveCallerDetails(_that);case _StartFakeCall() when startFakeCall != null:
return startFakeCall(_that);case _CancelFakeCall() when cancelFakeCall != null:
return cancelFakeCall(_that);case _ShowIncomingCall() when showIncomingCall != null:
return showIncomingCall(_that);case _AnswerCall() when answerCall != null:
return answerCall(_that);case _DeclineCall() when declineCall != null:
return declineCall(_that);case _EndCall() when endCall != null:
return endCall(_that);case _UpdateCallDuration() when updateCallDuration != null:
return updateCallDuration(_that);case _Reset() when reset != null:
return reset(_that);case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// Callbacks receives the raw object, upcasted.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case final Subclass2 value:
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult map<TResult extends Object?>({required TResult Function( _Initialize value)  initialize,required TResult Function( _SetCallerName value)  setCallerName,required TResult Function( _SetCallerNumber value)  setCallerNumber,required TResult Function( _SetCallerImage value)  setCallerImage,required TResult Function( _SetTimerDuration value)  setTimerDuration,required TResult Function( _SaveCallerDetails value)  saveCallerDetails,required TResult Function( _StartFakeCall value)  startFakeCall,required TResult Function( _CancelFakeCall value)  cancelFakeCall,required TResult Function( _ShowIncomingCall value)  showIncomingCall,required TResult Function( _AnswerCall value)  answerCall,required TResult Function( _DeclineCall value)  declineCall,required TResult Function( _EndCall value)  endCall,required TResult Function( _UpdateCallDuration value)  updateCallDuration,required TResult Function( _Reset value)  reset,}){
final _that = this;
switch (_that) {
case _Initialize():
return initialize(_that);case _SetCallerName():
return setCallerName(_that);case _SetCallerNumber():
return setCallerNumber(_that);case _SetCallerImage():
return setCallerImage(_that);case _SetTimerDuration():
return setTimerDuration(_that);case _SaveCallerDetails():
return saveCallerDetails(_that);case _StartFakeCall():
return startFakeCall(_that);case _CancelFakeCall():
return cancelFakeCall(_that);case _ShowIncomingCall():
return showIncomingCall(_that);case _AnswerCall():
return answerCall(_that);case _DeclineCall():
return declineCall(_that);case _EndCall():
return endCall(_that);case _UpdateCallDuration():
return updateCallDuration(_that);case _Reset():
return reset(_that);case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `map` that fallback to returning `null`.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case final Subclass value:
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? mapOrNull<TResult extends Object?>({TResult? Function( _Initialize value)?  initialize,TResult? Function( _SetCallerName value)?  setCallerName,TResult? Function( _SetCallerNumber value)?  setCallerNumber,TResult? Function( _SetCallerImage value)?  setCallerImage,TResult? Function( _SetTimerDuration value)?  setTimerDuration,TResult? Function( _SaveCallerDetails value)?  saveCallerDetails,TResult? Function( _StartFakeCall value)?  startFakeCall,TResult? Function( _CancelFakeCall value)?  cancelFakeCall,TResult? Function( _ShowIncomingCall value)?  showIncomingCall,TResult? Function( _AnswerCall value)?  answerCall,TResult? Function( _DeclineCall value)?  declineCall,TResult? Function( _EndCall value)?  endCall,TResult? Function( _UpdateCallDuration value)?  updateCallDuration,TResult? Function( _Reset value)?  reset,}){
final _that = this;
switch (_that) {
case _Initialize() when initialize != null:
return initialize(_that);case _SetCallerName() when setCallerName != null:
return setCallerName(_that);case _SetCallerNumber() when setCallerNumber != null:
return setCallerNumber(_that);case _SetCallerImage() when setCallerImage != null:
return setCallerImage(_that);case _SetTimerDuration() when setTimerDuration != null:
return setTimerDuration(_that);case _SaveCallerDetails() when saveCallerDetails != null:
return saveCallerDetails(_that);case _StartFakeCall() when startFakeCall != null:
return startFakeCall(_that);case _CancelFakeCall() when cancelFakeCall != null:
return cancelFakeCall(_that);case _ShowIncomingCall() when showIncomingCall != null:
return showIncomingCall(_that);case _AnswerCall() when answerCall != null:
return answerCall(_that);case _DeclineCall() when declineCall != null:
return declineCall(_that);case _EndCall() when endCall != null:
return endCall(_that);case _UpdateCallDuration() when updateCallDuration != null:
return updateCallDuration(_that);case _Reset() when reset != null:
return reset(_that);case _:
  return null;

}
}
/// A variant of `when` that fallback to an `orElse` callback.
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return orElse();
/// }
/// ```

@optionalTypeArgs TResult maybeWhen<TResult extends Object?>({TResult Function()?  initialize,TResult Function( String name)?  setCallerName,TResult Function( String number)?  setCallerNumber,TResult Function( String? imagePath)?  setCallerImage,TResult Function( int seconds)?  setTimerDuration,TResult Function()?  saveCallerDetails,TResult Function()?  startFakeCall,TResult Function()?  cancelFakeCall,TResult Function()?  showIncomingCall,TResult Function()?  answerCall,TResult Function()?  declineCall,TResult Function()?  endCall,TResult Function( int seconds)?  updateCallDuration,TResult Function()?  reset,required TResult orElse(),}) {final _that = this;
switch (_that) {
case _Initialize() when initialize != null:
return initialize();case _SetCallerName() when setCallerName != null:
return setCallerName(_that.name);case _SetCallerNumber() when setCallerNumber != null:
return setCallerNumber(_that.number);case _SetCallerImage() when setCallerImage != null:
return setCallerImage(_that.imagePath);case _SetTimerDuration() when setTimerDuration != null:
return setTimerDuration(_that.seconds);case _SaveCallerDetails() when saveCallerDetails != null:
return saveCallerDetails();case _StartFakeCall() when startFakeCall != null:
return startFakeCall();case _CancelFakeCall() when cancelFakeCall != null:
return cancelFakeCall();case _ShowIncomingCall() when showIncomingCall != null:
return showIncomingCall();case _AnswerCall() when answerCall != null:
return answerCall();case _DeclineCall() when declineCall != null:
return declineCall();case _EndCall() when endCall != null:
return endCall();case _UpdateCallDuration() when updateCallDuration != null:
return updateCallDuration(_that.seconds);case _Reset() when reset != null:
return reset();case _:
  return orElse();

}
}
/// A `switch`-like method, using callbacks.
///
/// As opposed to `map`, this offers destructuring.
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case Subclass2(:final field2):
///     return ...;
/// }
/// ```

@optionalTypeArgs TResult when<TResult extends Object?>({required TResult Function()  initialize,required TResult Function( String name)  setCallerName,required TResult Function( String number)  setCallerNumber,required TResult Function( String? imagePath)  setCallerImage,required TResult Function( int seconds)  setTimerDuration,required TResult Function()  saveCallerDetails,required TResult Function()  startFakeCall,required TResult Function()  cancelFakeCall,required TResult Function()  showIncomingCall,required TResult Function()  answerCall,required TResult Function()  declineCall,required TResult Function()  endCall,required TResult Function( int seconds)  updateCallDuration,required TResult Function()  reset,}) {final _that = this;
switch (_that) {
case _Initialize():
return initialize();case _SetCallerName():
return setCallerName(_that.name);case _SetCallerNumber():
return setCallerNumber(_that.number);case _SetCallerImage():
return setCallerImage(_that.imagePath);case _SetTimerDuration():
return setTimerDuration(_that.seconds);case _SaveCallerDetails():
return saveCallerDetails();case _StartFakeCall():
return startFakeCall();case _CancelFakeCall():
return cancelFakeCall();case _ShowIncomingCall():
return showIncomingCall();case _AnswerCall():
return answerCall();case _DeclineCall():
return declineCall();case _EndCall():
return endCall();case _UpdateCallDuration():
return updateCallDuration(_that.seconds);case _Reset():
return reset();case _:
  throw StateError('Unexpected subclass');

}
}
/// A variant of `when` that fallback to returning `null`
///
/// It is equivalent to doing:
/// ```dart
/// switch (sealedClass) {
///   case Subclass(:final field):
///     return ...;
///   case _:
///     return null;
/// }
/// ```

@optionalTypeArgs TResult? whenOrNull<TResult extends Object?>({TResult? Function()?  initialize,TResult? Function( String name)?  setCallerName,TResult? Function( String number)?  setCallerNumber,TResult? Function( String? imagePath)?  setCallerImage,TResult? Function( int seconds)?  setTimerDuration,TResult? Function()?  saveCallerDetails,TResult? Function()?  startFakeCall,TResult? Function()?  cancelFakeCall,TResult? Function()?  showIncomingCall,TResult? Function()?  answerCall,TResult? Function()?  declineCall,TResult? Function()?  endCall,TResult? Function( int seconds)?  updateCallDuration,TResult? Function()?  reset,}) {final _that = this;
switch (_that) {
case _Initialize() when initialize != null:
return initialize();case _SetCallerName() when setCallerName != null:
return setCallerName(_that.name);case _SetCallerNumber() when setCallerNumber != null:
return setCallerNumber(_that.number);case _SetCallerImage() when setCallerImage != null:
return setCallerImage(_that.imagePath);case _SetTimerDuration() when setTimerDuration != null:
return setTimerDuration(_that.seconds);case _SaveCallerDetails() when saveCallerDetails != null:
return saveCallerDetails();case _StartFakeCall() when startFakeCall != null:
return startFakeCall();case _CancelFakeCall() when cancelFakeCall != null:
return cancelFakeCall();case _ShowIncomingCall() when showIncomingCall != null:
return showIncomingCall();case _AnswerCall() when answerCall != null:
return answerCall();case _DeclineCall() when declineCall != null:
return declineCall();case _EndCall() when endCall != null:
return endCall();case _UpdateCallDuration() when updateCallDuration != null:
return updateCallDuration(_that.seconds);case _Reset() when reset != null:
return reset();case _:
  return null;

}
}

}

/// @nodoc


class _Initialize implements FakeCallEvent {
  const _Initialize();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Initialize);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.initialize()';
}


}




/// @nodoc


class _SetCallerName implements FakeCallEvent {
  const _SetCallerName({required this.name});
  

 final  String name;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetCallerNameCopyWith<_SetCallerName> get copyWith => __$SetCallerNameCopyWithImpl<_SetCallerName>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetCallerName&&(identical(other.name, name) || other.name == name));
}


@override
int get hashCode => Object.hash(runtimeType,name);

@override
String toString() {
  return 'FakeCallEvent.setCallerName(name: $name)';
}


}

/// @nodoc
abstract mixin class _$SetCallerNameCopyWith<$Res> implements $FakeCallEventCopyWith<$Res> {
  factory _$SetCallerNameCopyWith(_SetCallerName value, $Res Function(_SetCallerName) _then) = __$SetCallerNameCopyWithImpl;
@useResult
$Res call({
 String name
});




}
/// @nodoc
class __$SetCallerNameCopyWithImpl<$Res>
    implements _$SetCallerNameCopyWith<$Res> {
  __$SetCallerNameCopyWithImpl(this._self, this._then);

  final _SetCallerName _self;
  final $Res Function(_SetCallerName) _then;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? name = null,}) {
  return _then(_SetCallerName(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _SetCallerNumber implements FakeCallEvent {
  const _SetCallerNumber({required this.number});
  

 final  String number;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetCallerNumberCopyWith<_SetCallerNumber> get copyWith => __$SetCallerNumberCopyWithImpl<_SetCallerNumber>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetCallerNumber&&(identical(other.number, number) || other.number == number));
}


@override
int get hashCode => Object.hash(runtimeType,number);

@override
String toString() {
  return 'FakeCallEvent.setCallerNumber(number: $number)';
}


}

/// @nodoc
abstract mixin class _$SetCallerNumberCopyWith<$Res> implements $FakeCallEventCopyWith<$Res> {
  factory _$SetCallerNumberCopyWith(_SetCallerNumber value, $Res Function(_SetCallerNumber) _then) = __$SetCallerNumberCopyWithImpl;
@useResult
$Res call({
 String number
});




}
/// @nodoc
class __$SetCallerNumberCopyWithImpl<$Res>
    implements _$SetCallerNumberCopyWith<$Res> {
  __$SetCallerNumberCopyWithImpl(this._self, this._then);

  final _SetCallerNumber _self;
  final $Res Function(_SetCallerNumber) _then;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? number = null,}) {
  return _then(_SetCallerNumber(
number: null == number ? _self.number : number // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _SetCallerImage implements FakeCallEvent {
  const _SetCallerImage({required this.imagePath});
  

 final  String? imagePath;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetCallerImageCopyWith<_SetCallerImage> get copyWith => __$SetCallerImageCopyWithImpl<_SetCallerImage>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetCallerImage&&(identical(other.imagePath, imagePath) || other.imagePath == imagePath));
}


@override
int get hashCode => Object.hash(runtimeType,imagePath);

@override
String toString() {
  return 'FakeCallEvent.setCallerImage(imagePath: $imagePath)';
}


}

/// @nodoc
abstract mixin class _$SetCallerImageCopyWith<$Res> implements $FakeCallEventCopyWith<$Res> {
  factory _$SetCallerImageCopyWith(_SetCallerImage value, $Res Function(_SetCallerImage) _then) = __$SetCallerImageCopyWithImpl;
@useResult
$Res call({
 String? imagePath
});




}
/// @nodoc
class __$SetCallerImageCopyWithImpl<$Res>
    implements _$SetCallerImageCopyWith<$Res> {
  __$SetCallerImageCopyWithImpl(this._self, this._then);

  final _SetCallerImage _self;
  final $Res Function(_SetCallerImage) _then;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? imagePath = freezed,}) {
  return _then(_SetCallerImage(
imagePath: freezed == imagePath ? _self.imagePath : imagePath // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _SetTimerDuration implements FakeCallEvent {
  const _SetTimerDuration({required this.seconds});
  

 final  int seconds;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetTimerDurationCopyWith<_SetTimerDuration> get copyWith => __$SetTimerDurationCopyWithImpl<_SetTimerDuration>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetTimerDuration&&(identical(other.seconds, seconds) || other.seconds == seconds));
}


@override
int get hashCode => Object.hash(runtimeType,seconds);

@override
String toString() {
  return 'FakeCallEvent.setTimerDuration(seconds: $seconds)';
}


}

/// @nodoc
abstract mixin class _$SetTimerDurationCopyWith<$Res> implements $FakeCallEventCopyWith<$Res> {
  factory _$SetTimerDurationCopyWith(_SetTimerDuration value, $Res Function(_SetTimerDuration) _then) = __$SetTimerDurationCopyWithImpl;
@useResult
$Res call({
 int seconds
});




}
/// @nodoc
class __$SetTimerDurationCopyWithImpl<$Res>
    implements _$SetTimerDurationCopyWith<$Res> {
  __$SetTimerDurationCopyWithImpl(this._self, this._then);

  final _SetTimerDuration _self;
  final $Res Function(_SetTimerDuration) _then;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? seconds = null,}) {
  return _then(_SetTimerDuration(
seconds: null == seconds ? _self.seconds : seconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class _SaveCallerDetails implements FakeCallEvent {
  const _SaveCallerDetails();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SaveCallerDetails);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.saveCallerDetails()';
}


}




/// @nodoc


class _StartFakeCall implements FakeCallEvent {
  const _StartFakeCall();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _StartFakeCall);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.startFakeCall()';
}


}




/// @nodoc


class _CancelFakeCall implements FakeCallEvent {
  const _CancelFakeCall();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CancelFakeCall);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.cancelFakeCall()';
}


}




/// @nodoc


class _ShowIncomingCall implements FakeCallEvent {
  const _ShowIncomingCall();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ShowIncomingCall);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.showIncomingCall()';
}


}




/// @nodoc


class _AnswerCall implements FakeCallEvent {
  const _AnswerCall();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AnswerCall);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.answerCall()';
}


}




/// @nodoc


class _DeclineCall implements FakeCallEvent {
  const _DeclineCall();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DeclineCall);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.declineCall()';
}


}




/// @nodoc


class _EndCall implements FakeCallEvent {
  const _EndCall();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _EndCall);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.endCall()';
}


}




/// @nodoc


class _UpdateCallDuration implements FakeCallEvent {
  const _UpdateCallDuration({required this.seconds});
  

 final  int seconds;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateCallDurationCopyWith<_UpdateCallDuration> get copyWith => __$UpdateCallDurationCopyWithImpl<_UpdateCallDuration>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateCallDuration&&(identical(other.seconds, seconds) || other.seconds == seconds));
}


@override
int get hashCode => Object.hash(runtimeType,seconds);

@override
String toString() {
  return 'FakeCallEvent.updateCallDuration(seconds: $seconds)';
}


}

/// @nodoc
abstract mixin class _$UpdateCallDurationCopyWith<$Res> implements $FakeCallEventCopyWith<$Res> {
  factory _$UpdateCallDurationCopyWith(_UpdateCallDuration value, $Res Function(_UpdateCallDuration) _then) = __$UpdateCallDurationCopyWithImpl;
@useResult
$Res call({
 int seconds
});




}
/// @nodoc
class __$UpdateCallDurationCopyWithImpl<$Res>
    implements _$UpdateCallDurationCopyWith<$Res> {
  __$UpdateCallDurationCopyWithImpl(this._self, this._then);

  final _UpdateCallDuration _self;
  final $Res Function(_UpdateCallDuration) _then;

/// Create a copy of FakeCallEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? seconds = null,}) {
  return _then(_UpdateCallDuration(
seconds: null == seconds ? _self.seconds : seconds // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

/// @nodoc


class _Reset implements FakeCallEvent {
  const _Reset();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Reset);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'FakeCallEvent.reset()';
}


}




// dart format on
