# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/
backend/node_modules/
# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VS Code (uncomment if you don’t want to track it)
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio build artifacts
/android/app/debug
/android/app/profile
/android/app/release

# ===========================
# Node.js / Express related
# ===========================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
package-lock.json
pnpm-lock.yaml
yarn.lock

# Environment variables
.env
backend/.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dist / Build output
/dist
/.next
/out
/build
coverage/
.cache/

# Logs
logs/
*.log
debug.log

# OS generated files
Thumbs.db
