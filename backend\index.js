import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import connectDB from "./config/db.js";
import authRoutes from "./routes/auth.routes.js";
import userRoutes from "./routes/user.routes.js";
import emergencyRoutes from "./routes/emergency.routes.js";
import sosRoutes from "./routes/sos.router.js";


dotenv.config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middlewares
app.use(cors({ origin: "*" }));
app.use(express.json());


app.use("/api/auth", authRoutes);
app.use("/api/users", userRoutes);
app.use("/api/users", emergencyRoutes);
app.use("/api/sos", sosRoutes);

// Test route
app.get("/", (req, res) => {
  res.send("Backend connected successfully ✅");
});


app.listen(PORT, () => 
    {
        console.log(`Server running on port ${PORT}`)
        connectDB();
    });
