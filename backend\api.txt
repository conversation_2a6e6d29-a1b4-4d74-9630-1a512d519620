cd backend
npm install
npm run dev

https://everest-hackathon.onrender.com/api/auth/send-otp

request:{
    "phoneNumber":"+919347206960"
}

response:{
    "message": "OTP sent successfully",
    "sid": "VE6d9c8f376d7dc78a7bc79d96a90311c5"
}



https://everest-hackathon.onrender.com/api/auth/verify-otp


request:{
    "phoneNumber":"+919347206960",
    "otp":"761610"
}

response:{
    "message": "OTP verified successfully",
    "userId": "68e258409e2279a8a28fbb2f",
    "user": {
        "phoneNumber": "+919347206960",
        "isProfileComplete": false,
        "isVerified": true,
        "_id": "68e258409e2279a8a28fbb2f",
        "emergencyContacts": [],
        "createdAt": "2025-10-05T11:36:32.994Z",
        "__v": 0
    },
    "isProfileComplete": false
}




https://everest-hackathon.onrender.com/api/users/{userId}

request :{
   "name":"Meda Harsha Sri",
   "email":"<EMAIL>",
   "Address":"Nellore district",
   "emergencyContacts":[{
    "name":"Subramanyam",
    "phoneNumber":"+91 9347325589",
    "relationship":"Father"
   }]
}

response : {
    "message": "Profile updated successfully",
    "user": {
        "_id": "68e258409e2279a8a28fbb2f",
        "phoneNumber": "+919347206960",
        "isProfileComplete": true,
        "isVerified": true,
        "emergencyContacts": [
            {
                "name": "Subramanyam",
                "phoneNumber": "+91 9347325589",
                "relationship": "Father",
                "isPrimary": false,
                "canReceiveSosAlerts": true,
                "canTrackLocation": false,
                "_id": "68e323c8afffed643145eace"
            }
        ],
        "createdAt": "2025-10-05T11:36:32.994Z",
        "__v": 0,
        "email": "<EMAIL>",
        "name": "Meda Harsha Sri"
    }
}



https://everest-hackathon.onrender.com/api/users/get-emergency-contacts/userId

request:{}
response:{
    "success": true,
    "data": [
        {
            "user": "68e62b776adc8ea103f8f221",
            "name": "Subramanyam",
            "phoneNumber": "+919347206960",
            "_id": "68e6371bfd2a6032366715ba"
        }
    ]
}



https://everest-hackathon.onrender.com/api/users/add-emergency-contact

request:
    {
    "userId":"68e258409e2279a8a28fbb2f",
    "name":"Subramanyam",
    "phoneNumber":"+919347206960",
    "relationship":"Father"
}

response:{
    "success": true,
    "message": "Emergency contact added and notified successfully",
    "data": {
        "name": "Subramanyam",
        "phoneNumber": "+919347206960",
        "user": "68e258409e2279a8a28fbb2f"
    }
}


https://everest-hackathon.onrender.com/api/users/update/UserId/ContactId

request:{
    "name": "Riya Singh",
  "relationship": "Cousin",
  "phoneNumber":"+919347206960"
}

response:{
 "success": true,
    "message": "Emergency contact updated successfully",
    "data": {
        "contact_id": "68e332921a5a274650bb7a4f",
        "name": "Riya Singh",
        "phoneNumber": "+91 9347325589"
    }
}


https://everest-hackathon.onrender.com/api/users/delete/UserId/ContactId

request:No request

response:{
    "success": true,
    "message": "Emergency contact deleted successfully"
}

https://everest-hackathon.onrender.com/api/sos/send-sos-alert

{
  "username": "Harsha",
  "location": "https://maps.google.com/?q=17.3850,78.4867",
  "phoneNumbers": ["+919347206960", "+919182028713"]
}


response:{
    "success": true,
    "message": "SOS alerts processed",
    "results": [
        {
            "number": "+919347206960",
            "status": "sent",
            "sid": "SM85253b72191a0245e65436a9d97dc8a9"
        },
        {
            "number": "+919182028713",
            "status": "sent",
            "sid": "SM674be6570449a48b2ecee889c27b65dd"
        }
    ]
}

